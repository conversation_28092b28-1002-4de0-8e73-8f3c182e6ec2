import React, { useState, useEffect, useRef } from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useLocation, useNavigate } from 'react-router-dom';
import { ThemeProvider, useTheme } from './contexts/ThemeContext';
import { DataProvider } from './contexts/DataContext';

// Import screens
import CanvasScreen from './screens/CanvasScreen';
import ProductsScreen from './screens/ProductsScreen';
import SettingsScreen from './screens/SettingsScreen';
import ExportScreen from './screens/ExportScreen';
import HelpScreen from './screens/HelpScreen';
import InvoicePdfScreen from './screens/InvoicePdfScreen';

// Import styles
import './App.css';

function AppContent() {
  const location = useLocation();
  const navigate = useNavigate();
  const { isDark } = useTheme();
  const [showMenu, setShowMenu] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);

  const navigationItems = [
    { path: '/', label: 'Canvas', icon: '🎨' },
    { path: '/products', label: 'Products', icon: '📦' },
    { path: '/invoice-pdf', label: 'Invoice PDF', icon: '🧾' },
  ];

  const menuItems = [
    { path: '/', label: 'Canvas', icon: '🎨' },
    { path: '/products', label: 'Products', icon: '📦' },
    { path: '/settings', label: 'Settings', icon: '⚙️' },
    { path: '/export', label: 'Export', icon: '💾' },
    { path: '/help', label: 'Help & Support', icon: '❓' },
    { path: '/invoice-pdf', label: 'Invoice PDF', icon: '🧾' },
  ];

  const handleMenuItemClick = (path: string) => {
    navigate(path);
    setShowMenu(false);
  };

  return (
    <div className="app">
      {/* Sidebar Navigation */}
      <nav className="sidebar">
        <div className="sidebar-header">
          <h1 className="app-title">PlomDesign</h1>
        </div>

        <ul className="nav-list">
          {navigationItems.map((item) => (
            <li key={item.path} className="nav-item">
              <Link
                to={item.path}
                className={`nav-link ${location.pathname === item.path ? 'active' : ''}`}
              >
                <span className="nav-icon">{item.icon}</span>
                <span className="nav-label">{item.label}</span>
              </Link>
            </li>
          ))}
        </ul>
      </nav>

      {/* Main Content */}
      <main className={`main-content ${location.pathname === '/products' ? 'products' : ''}`}>
        {/* Header with Three Dots Menu */}
        <header className="main-header">
          {location.pathname !== '/products' && (
            <h1 className="page-title">
              {location.pathname === '/' && 'Canvas'}
              {location.pathname === '/settings' && 'Settings'}
              {location.pathname === '/export' && 'Export'}
              {location.pathname === '/help' && 'Help & Support'}
              {location.pathname === '/invoice-pdf' && 'Invoice PDF'}
            </h1>
          )}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', width: '100%' }}>
            {location.pathname === '/products' && (
              <>
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{
                    padding: '8px 12px',
                    borderRadius: '6px',
                    border: `1px solid ${isDark ? '#4b5563' : '#d1d5db'}`,
                    backgroundColor: isDark ? '#374151' : '#f9fafb',
                    color: isDark ? '#ffffff' : '#000000',
                    fontSize: '14px',
                    outline: 'none',
                    width: '100%',
                    flex: '1 1 auto',
                    minWidth: '140px',
                  }}
                />
                <button
                  style={{
                    backgroundColor: '#3b82f6',
                    color: '#ffffff',
                    border: 'none',
                    padding: '8px 12px',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '600',
                    whiteSpace: 'nowrap',
                  }}
                  onClick={() => setShowAddModal(true)}
                >
                  + Add
                </button>
              </>
            )}
            <div style={{ position: 'relative' }}>
              <button
                style={{
                  backgroundColor: isDark ? '#374151' : '#e5e7eb',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '24px',
                  color: isDark ? '#ffffff' : '#000000',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  fontFamily: 'Arial, sans-serif',
                }}
                onClick={() => setShowMenu(!showMenu)}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = isDark ? '#4b5563' : '#d1d5db';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = isDark ? '#374151' : '#e5e7eb';
                }}
              >
                ...
              </button>
              {showMenu && (
                <div
                  style={{
                    position: 'absolute',
                    top: '100%',
                    right: 0,
                    backgroundColor: isDark ? '#1e293b' : '#ffffff',
                    border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                    zIndex: 1000,
                    minWidth: '150px',
                  }}
                >
                  {menuItems.map((item, index) => (
                    <div
                      key={item.path}
                      style={{
                        padding: '12px 16px',
                        cursor: 'pointer',
                        color: isDark ? '#ffffff' : '#000000',
                        borderBottom: index < menuItems.length - 1 ? `1px solid ${isDark ? '#374151' : '#e5e7eb'}` : 'none',
                      }}
                      onClick={() => handleMenuItemClick(item.path)}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = isDark ? '#374151' : '#f3f4f6';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      {item.icon} {item.label}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </header>

        <Routes>
          <Route path="/" element={<CanvasScreen />} />
          <Route path="/products" element={<ProductsScreen searchTerm={searchTerm} showAddModal={showAddModal} onCloseAddModal={() => setShowAddModal(false)} />} />
          <Route path="/invoice-pdf" element={<InvoicePdfScreen />} />
          <Route path="/settings" element={<SettingsScreen />} />
          <Route path="/export" element={<ExportScreen />} />
          <Route path="/help" element={<HelpScreen />} />
        </Routes>
      </main>
    </div>
  );
}

function App() {
  return (
    <ThemeProvider>
      <DataProvider>
        <Router
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true,
          }}
        >
          <AppContent />
        </Router>
      </DataProvider>
    </ThemeProvider>
  );
}

export default App;